🌟 รายงานภาพรวมระบบการเทรน
================================================================================

📊 สถิติรวมทั้งระบบ:
   จำนวนโมเดลทั้งหมด: 2
   คะแนนเฉลี่ย: 77.86/100
   Win Rate เฉลี่ย (Test): 64.00%
   Expectancy เฉลี่ย (Test): 5.25

🏆 อันดับโมเดลที่ดีที่สุด (Top 5):
   1. GOLD M060: Score 78.8, Test W% 61.6%, Test Exp 4.63
   2. USDJPY M060: Score 76.9, Test W% 66.4%, Test Exp 5.86

🏗️ เปรียบเทียบตาม Architecture:
   multi_model: 2 โมเดล, Score 77.9, W% 64.0%, Exp 5.24

💰 เปรียบเทียบตาม Symbol:
   GOLD: 1 timeframes, Score 78.8, W% 61.6%
   USDJPY: 1 timeframes, Score 76.9, W% 66.4%

📈 แนวโน้มการพัฒนาระบบ:
   📉 แย่ลงเฉลี่ย 5.8 คะแนน
   📊 โมเดลที่ดีขึ้น: 0/2 (0.0%)

💡 คำแนะนำสำหรับระบบ:
   ✅ ระบบมีประสิทธิภาพดี - พร้อมใช้งานจริง

📅 อัปเดตล่าสุด: 2025-09-20 20:13:48
================================================================================