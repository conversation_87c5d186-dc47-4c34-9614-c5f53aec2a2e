{"GOLD_M60": [{"timestamp": "2025-09-20 19:51:14", "session_id": "2025-09-20_195114", "avg_accuracy": 0.958760637542786, "avg_f1_score": 0.5, "avg_auc": 0.8686608318923215, "total_scenarios": 2, "buy_metrics": {"count": 2750, "win_rate": 9.818181818181818, "expectancy": -11.440727272725933, "trade_accuracy": 0.09818181818181819, "trade_f1_score": 0.11781818181818182, "trade_auc": 0.5392727272727272}, "sell_metrics": {"count": 2696, "win_rate": 10.089020771513352, "expectancy": -3.3657270029685304, "trade_accuracy": 0.10089020771513352, "trade_f1_score": 0.12106824925816022, "trade_auc": 0.5403560830860534}}], "USDJPY_M60": [{"timestamp": "2025-09-20 20:13:58", "session_id": "2025-09-20_201358", "avg_accuracy": 0.9692086022323322, "avg_f1_score": 0.5, "avg_auc": 0.7621623925365383, "total_scenarios": 2, "buy_metrics": {"count": 1733, "win_rate": 9.751875360646277, "expectancy": -8.03635314483513, "trade_accuracy": 0.09751875360646277, "trade_f1_score": 0.11702250432775532, "trade_auc": 0.5390075014425851}, "sell_metrics": {"count": 2100, "win_rate": 8.857142857142856, "expectancy": -10.692857142858598, "trade_accuracy": 0.08857142857142856, "trade_f1_score": 0.10628571428571428, "trade_auc": 0.5354285714285715}}]}