โครงสร้างไฟล์
LightGBM/Hyper_Multi
└─ {timeframe}_{symbol}
    ├─{timeframe}_{symbol}_counter_trend_best_params
    ├─ {timeframe}_{symbol}_counter_trend_Buy_best_params
    ├─ {timeframe}_{symbol}_counter_trend_Buy_tuning_flag
    ├─ {timeframe}_{symbol}_counter_trend_Sell_best_params
    ├─ {timeframe}_{symbol}_counter_trend_Sell_tuning_flag
    ├─ {timeframe}_{symbol}_counter_trend_tuning_flag
    ├─ {timeframe}_{symbol}_trend_following_best_params
    ├─ {timeframe}_{symbol}_trend_following_Buy_best_params
    ├─ {timeframe}_{symbol}_trend_following_Buy_tuning_flag
    ├─ {timeframe}_{symbol}_trend_following_Sell_best_params
    ├─ {timeframe}_{symbol}_trend_following_Sell_tuning_flag
    └─ {timeframe}_{symbol}_trend_following_tuning_flag

// output
Parameter Stability Analysis (Multi-Architecture Support)
======================================================================
🔍 ตรวจสอบความเข้ากันได้กับ LightGBM_10_1.py...
✅ พบไฟล์หลัก: LightGBM_10_1.py
✅ พบโฟลเดอร์: LightGBM/Multi
✅ พบโฟลเดอร์: LightGBM/Hyper_Multi
⚠️ ไม่พบโฟลเดอร์: LightGBM_Multi
⚠️ ไม่พบโฟลเดอร์: LightGBM_Hyper_Multi
พบ Model Architectures: []

❌ ไม่พบข้อมูลพารามิเตอร์
กรุณารัน hyperparameter tuning ก่อน:
   python LightGBM_10_1.py  # ← ไฟล์หลักใหม่
   หรือ python LightGBM_03_Compare.py  # ← ไฟล์เก่า (ยังใช้ได้)