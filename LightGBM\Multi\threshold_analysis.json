{"analysis_date": "2025-09-20T20:13:58.871264", "total_models_analyzed": 12, "current_thresholds": {"min_accuracy": 0.5, "min_auc": 0.5, "min_f1": 0.1, "min_precision": 0.1, "min_recall": 0.5, "min_win_rate": 0.3, "min_expectancy": 25.0, "min_trades": 10, "improvement_threshold": 0.01, "max_decline_threshold": -0.02}, "recommended_thresholds": {"accuracy": {"threshold": 0.9425720574939018, "mean": 0.9642689681509563, "std": 0.0469212513569209, "min": 0.8624567474048442, "max": 0.9974655236675364, "count": 12}, "auc": {"threshold": 0.8304326169879422, "mean": 0.8617462756622377, "std": 0.04424996671907447, "min": 0.7693508114856429, "max": 0.9163003230126842, "count": 10}, "f1": {"threshold": 0.961886266676917, "mean": 0.9663911770383492, "std": 0.04624289258661424, "min": 0.8486613608585103, "max": 0.9967954841308139, "count": 11}, "precision": {"threshold": 0.9540578396014717, "mean": 0.9603765745841667, "std": 0.05314208223337702, "min": 0.8232046212295786, "max": 0.9961263441772689, "count": 11}, "recall": {"threshold": 0.9425720574939018, "mean": 0.9642689681509563, "std": 0.0469212513569209, "min": 0.8624567474048442, "max": 0.9974655236675364, "count": 12}, "win_rate": {"threshold": 0.65, "mean": 0.6500000000000001, "std": 1.1102230246251565e-16, "min": 0.65, "max": 0.65, "count": 12}, "expectancy": {"threshold": 48.339657898094806, "mean": 48.5404407196829, "std": 2.032569613497421, "min": 43.42609778633256, "max": 49.86174990177888, "count": 11}}, "new_thresholds": {"min_accuracy": 0.9425720574939018, "min_auc": 0.8304326169879422, "min_f1": 0.961886266676917, "min_precision": 0.9540578396014717, "min_recall": 0.9425720574939018, "min_win_rate": 0.006500000000000001, "min_expectancy": 48.339657898094806, "min_trades": 10, "improvement_threshold": 0.01, "max_decline_threshold": -0.02}, "detailed_results": {"GOLD_M60_trend_following": {"symbol": "GOLD", "timeframe": "M60", "scenario": "trend_following", "metrics": [{"accuracy": 0.920211960635882, "auc": 0.8926643037311575, "f1": 0.9076345397458857, "precision": 0.8969336250835593, "recall": 0.920211960635882}], "trading_stats": [{"win_rate": 0.65, "expectancy": 45.85678784835615, "num_trades": 0}]}, "GOLD_M60_counter_trend": {"symbol": "GOLD", "timeframe": "M60", "scenario": "counter_trend", "metrics": [{"accuracy": 0.8624567474048442, "auc": 0.82975010113599, "f1": 0.8134422793334668, "precision": 0.780885442412259, "recall": 0.8624567474048442}], "trading_stats": [{"win_rate": 0.65, "expectancy": 41.97403077874816, "num_trades": 0}]}, "GOLD_M60_trend_following_Buy": {"symbol": "GOLD", "timeframe": "M60", "scenario": "trend_following_Buy", "metrics": [{"accuracy": 0.9964705882352941, "auc": 0.9051886833705015, "f1": 0.9947090020451315, "precision": 0.992953633217993, "recall": 0.9964705882352941}], "trading_stats": [{"win_rate": 0.65, "expectancy": 49.78516040469775, "num_trades": 0}]}, "GOLD_M60_counter_trend_Buy": {"symbol": "GOLD", "timeframe": "M60", "scenario": "counter_trend_Buy", "metrics": [{"accuracy": 0.9916434540389972, "auc": 0.7693508114856429, "f1": 0.9874827122737987, "precision": 0.9833567399383928, "recall": 0.9916434540389972}], "trading_stats": [{"win_rate": 0.65, "expectancy": 49.498957866645895, "num_trades": 0}]}, "GOLD_M60_trend_following_Sell": {"symbol": "GOLD", "timeframe": "M60", "scenario": "trend_following_Sell", "metrics": [{"accuracy": 0.9963102527869367, "auc": 0.9163003230126842, "f1": 0.9946255093495291, "precision": 0.9929464540377346, "recall": 0.9963102527869367}], "trading_stats": [{"win_rate": 0.65, "expectancy": 49.77241436019537, "num_trades": 0}]}, "GOLD_M60_counter_trend_Sell": {"symbol": "GOLD", "timeframe": "M60", "scenario": "counter_trend_Sell", "metrics": [{"accuracy": 0.9907235621521335, "auc": 0.6647003745318353, "f1": 0.9861069565503063, "precision": 0.9815331766034125, "recall": 0.9907235621521335}], "trading_stats": [{"win_rate": 0.65, "expectancy": 49.40673612102184, "num_trades": 0}]}, "USDJPY_M60_trend_following": {"symbol": "USDJPY", "timeframe": "M60", "scenario": "trend_following", "metrics": [{"accuracy": 0.9500254231132418, "auc": 0.8873979800389487, "f1": 0.9376655768035277, "precision": 0.9265825025995309, "recall": 0.9500254231132418}], "trading_stats": [{"win_rate": 0.65, "expectancy": 47.272579675167776, "num_trades": 0}]}, "USDJPY_M60_counter_trend": {"symbol": "USDJPY", "timeframe": "M60", "scenario": "counter_trend", "metrics": [{"accuracy": 0.8831521739130435, "auc": 0.8191606068881736, "f1": 0.8486613608585103, "precision": 0.8232046212295786, "recall": 0.8831521739130435}], "trading_stats": [{"win_rate": 0.65, "expectancy": 43.42609778633256, "num_trades": 0}]}, "USDJPY_M60_trend_following_Buy": {"symbol": "USDJPY", "timeframe": "M60", "scenario": "trend_following_Buy", "metrics": [{"accuracy": 0.9974655236675364, "auc": 0.8324801645437989, "f1": 0.9967954841308139, "precision": 0.9961263441772689, "recall": 0.9974655236675364}], "trading_stats": [{"win_rate": 0.65, "expectancy": 49.8515020101985, "num_trades": 0}]}, "USDJPY_M60_counter_trend_Buy": {"symbol": "USDJPY", "timeframe": "M60", "scenario": "counter_trend_Buy", "metrics": [{"accuracy": 0.9913127413127413, "auc": 0.8811606275163127, "f1": 0.9889101413580329, "precision": 0.9865191593914998, "recall": 0.9913127413127413}], "trading_stats": [{"win_rate": 0.65, "expectancy": 49.55620903295322, "num_trades": 0}]}, "USDJPY_M60_trend_following_Sell": {"symbol": "USDJPY", "timeframe": "M60", "scenario": "trend_following_Sell", "metrics": [{"accuracy": 0.9971803813905171, "auc": 0.8840091548991665, "f1": 0.996291232588564, "precision": 0.995403668016102, "recall": 0.9971803813905171}], "trading_stats": [{"win_rate": 0.65, "expectancy": 49.86174990177888, "num_trades": 0}]}, "USDJPY_M60_counter_trend_Sell": {"symbol": "USDJPY", "timeframe": "M60", "scenario": "counter_trend_Sell", "metrics": [{"accuracy": 0.9942748091603053, "auc": 0.5844529750479847, "f1": 0.9914204317177399, "precision": 0.9885823961307615, "recall": 0.9942748091603053}], "trading_stats": [{"win_rate": 0.65, "expectancy": 49.65665290916395, "num_trades": 0}]}}}