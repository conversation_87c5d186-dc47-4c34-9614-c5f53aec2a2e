ต้องการหาค่าสถิติด้านการเงิน DDmax, Profit จากการใช้ขนาด lot คงที่ 1.0 
เพื่อหาทุนที่ต้องใช้ โดยมีปัจจัยเพิ่ม pips value และ margin
เมื่อคำนวณเสร็จให้มีการบันทึก จากขั้นตอน create_trade_cycles_with_model() เพื่อไว้ดึงมารวมกันภายหลัง ควรบันทึกอย่างไร ช่วยแนะนำ

เมื่อคำนวณแยกเสร็จ เช่น ตอนนี้คำนวน EURUSD
แต่ต้องการรวม AUDUSD EURUSD GBPUSD GOLD NZDUSD USDCAD USDJPY << ได้จาก TEST_GROUPS ทั้ง M30 และ M60
นำมาเรียงต่อกันจากขั้นตอนการทำงาน
1. ตอนเปิด ต้องใช้ทุนในการเปิด margin แสดงว่าต้องบันทึกเวลาเปิด
2. ตอนปิด ต้องใช้กำไร/ขาดทุน ที่คูณด้วย pips value เพื่อแปลงเป็นสกุลเงินเดียวกัน แสดงว่าต้องบันทึกเวลาปิด
3. เมื่อได้ชุดข้อมูลทั้งหมด บันทึกไฟล์ บันทึกไฟล์แบบไหนแนะนำด้วย
4. สร้างชุดข้อมูล โดยรวมข้อมูลทั้งหมดเข้าด้วยกัน แล้ว plot.png เรียงตามเวลาที่ เปิด/ปิด
5. เป็นกราฟเพื่อดู DDmax, Profit, 
6. คำนวณ และวิเคราะห์ หาเงินทุกที่ต้องใช้กรณีเปิดขนาด  lot คงที่ 1.0
7. ต้องการหาว่าถ้าสมมุติมีเงินทุน 1000 USD (สมมุติ) และตั้งค่า DD ไม่เกิน 2% ต่อวัน และ ไม่เกิน 5% สัปดาห์ และ 10% ของทั้งหมด ฉันจะเปิดที่ความเสียงได้ที่เท่าไหร่ เช่น ได้มากสุด 0.25% หรืออาจทำเป็นตารางให้ก็ได้

ตัวอย่างการคำนวณที่ฉันใช้งาน

การคำนวน pips value มี 3 แบบหลักๆ
1. GOLD
2. EURUSD หรือ USD คือ quote currency
3. USDJPY หรือ USD คือ base currency

Leverage = 1:500 หรือ 500 ค่าเริ่มต้นไว้ก่อน
สกุลเงินหลัก USD

1. คำนวน GOLD หรือ XAUUSD
Bid 3683.25
point 0.01
Contract Size 100 < จะต่างจากคู่เงิน ขนาดสัญญามาตรฐาน (100 ออนซ์)

pips value = ทศนิยม x อัตราแลกเปลี่ยน x ขนาดสัญญา / อัตราแลกเปลี่ยน
pips value = 0.01/3683.25 * 3683.25 * 100 = 1.0 ต่อจุด หรือ 10 ต่อ pips

margin = (ขนาดล็อต x ขนาดสัญญา x อัตราแลกเปลี่ยน) / Leverage
margin = 0.01 * 100 * 3683.25 / 500 = 7.367

2. คำนวน EURUSD
Bid 1.18525
point 0.00001
Contract Size 100,000
pips value = 0.00001/1.18525 * 1.18525 * 100,000 = 1.0 ต่อจุด หรือ 10 ต่อ pips
margin = 0.01 * 100,000 * 1.18525 / 500 = 2.371

3. คำนวน USDJPY
Bid 146.324
point 0.001
Contract Size 100,000
pips value = 0.001/146.324 * 100,000 = 0.683 ต่อจุด หรือ 6.83 ต่อ pips
margin = 0.01 * 100,000 / 500 = 2.00