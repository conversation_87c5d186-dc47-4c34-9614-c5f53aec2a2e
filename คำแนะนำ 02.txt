﻿ปัญหาที่พบจากการวิเคราะห์
1.ความไม่สมดุลของข้อมูล:
Buy Trades มี Win% 0% และ Expectancy -178.00 แสดงว่าโมเดลไม่สามารถทำนาย Buy Trades ได้เลย
มีการใช้ Binary Classification แทน Multi-class เนื่องจากมีเพียง 1 class ที่มีข้อมูลเพียงพอ
2.จำนวนข้อมูลน้อยเกินไป:
มีเพียง 50 รายการซื้อขายในชุดข้อมูล ซึ่งน้อยเกินไปสำหรับการเทรนโมเดล ML ที่มีประสิทธิภาพ ถ้าอย่างงั้นต้องเท่าไหร่ถึงจะพอ
3.การตั้งค่า Threshold:
ใช้ reduce_threshold 0.8 (threshold จริง 0.5200) ซึ่งอาจไม่เหมาะสมกับลักษณะข้อมูล 
4.การเลือก Features:
มีการตัด Features บางตัวออกเนื่องจาก VIF สูง ซึ่งอาจตัด Features ที่สำคัญออกไป แล้วจะใช้อะไรเพื่อวัดผล
มีการใช้ Features จำเป็น 22 ตัวจากไฟล์ M60_must_have_features.pkl

แนวทางการพัฒนาโมเดลให้มีประสิทธิภาพมากขึ้น
1.เพิ่มปริมาณข้อมูล:
รวบรวมข้อมูลเพิ่มเติมเพื่อให้มีตัวอย่างมากกว่า 50 รายการ
พิจารณาใช้เทคนิค Data Augmentation หรือ Synthetic Data Generation สำหรับ Financial Time Series
2.แก้ไขปัญหา Class Imbalance:
ใช้เทคนิค SMOTE หรือ ADASYN เพื่อสร้างข้อมูลสังเคราะห์สำหรับ Buy Trades ที่มีน้อย
ปรับ class_weight ใน LightGBM เพื่อให้น้ำหนักกับ class ที่มีข้อมูลน้อย
3.ปรับปรุงพารามิเตอร์ของ LightGBM:
ลดความซับซ้อนของโมเดลเพื่อป้องกัน Overfitting เนื่องจากข้อมูลน้อย:
ลด max_depth
เพิ่ม min_data_in_leaf
เพิ่ม lambda_l1 และ lambda_l2 สำหรับ regularization
ใช้ Early Stopping เพื่อหยุดการเทรนเมื่อไม่มีการพัฒนา
ต้องแก้ไขส่วนไหน เนื่องจากมีการตั้งค่าหลายที่
4.ปรับปรุงการเลือก Features:
ใช้ Feature Selection ที่พิจารณาความสัมพันธ์กับ Target มากกว่าการตัดด้วย VIF เพียงอย่างเดียว
พิจารณาใช้ Feature Engineering เพื่อสร้าง Features ใหม่ที่มีความสัมพันธ์กับ Target มากขึ้น
ทดลองใช้ Recursive Feature Elimination (RFE) หรือ Sequential Feature Selection
5.ปรับ Threshold ในการตัดสินใจ:
ทดลองปรับ threshold จาก 0.52 เป็นค่าอื่นๆ โดยใช้ ROC curve หรือ Precision-Recall curve
พิจารณาใช้ค่า threshold ที่แตกต่างกันสำหรับ Buy และ Sell Trades
6.ใช้เทคนิค Cross-Validation ที่เหมาะสมกับ Time Series:
ใช้ Time Series Cross-Validation แทนการแบ่งข้อมูลแบบสุ่ม
ทดสอบโมเดลบนหลายช่วงเวลาเพื่อประเมินความเสถียร
7.พิจารณาใช้โมเดลอื่นเพิ่มเติม:
ทดลองใช้ Ensemble ของหลายโมเดล (LightGBM, XGBoost, CatBoost)
พิจารณาใช้ Deep Learning สำหรับ Time Series หากมีข้อมูลเพียงพอ
การปรับปรุงเหล่านี้ควรช่วยเพิ่ม Win% และ Expectancy ของโมเดลได้ โดยเฉพาะการแก้ไขปัญหา Buy Trades ที่มี Win% เป็น 0% ซึ่งเป็นปัญหาหลักที่ทำให้ประสิทธิภาพโดยรวมต่ำ

## แนวทางการปรับปรุงประสิทธิภาพโมเดล
ช่วยตรวจสอบ ส่วนไหนที่คิดว่าทำให้การเทรนดีขึ้น
และถ้าคิดว่าดี ช่วยตรวจสอบว่ามีการทำงานแบบนั้น (ตามข้อที่เสนอ) แล้วหรือยัง ถ้ายังช่วยปรับปรุงและแก้ไข code ให้ทำงาน สอดคล้องกับ code เดิมได้
ใช้เป็นแนวทาง..

1. ปรับปรุงการจัดการ Class Imbalance
# เพิ่มการใช้ SMOTE ในฟังก์ชัน train_scenario_model
from imblearn.over_sampling import SMOTE
# ในส่วนการเตรียมข้อมูล
if use_smote and len(np.unique(y_train)) > 1:
    smote = SMOTE(random_state=42)
    X_train_resampled, y_train_resampled = smote.fit_resample(X_train, y_train)
    print(f"✅ ใช้ SMOTE: {len(X_train)} → {len(X_train_resampled)} samples")
    X_train, y_train = X_train_resampled, y_train_resampled

2.ปรับปรุงการเลือก Features
# เพิ่มการทำ Recursive Feature Elimination
from sklearn.feature_selection import RFE
# ในฟังก์ชัน train_scenario_model
if perform_feature_selection and len(X_train.columns) > 20:
    print(f"🔍 ทำ Recursive Feature Elimination...")
    estimator = LGBMClassifier(random_state=42)
    selector = RFE(estimator, n_features_to_select=min(20, len(X_train.columns)//2), step=0.1)
    selector.fit(X_train, y_train)
    selected_features = X_train.columns[selector.support_].tolist()
    X_train = X_train[selected_features]
    X_test = X_test[selected_features]
    print(f"✅ เลือก {len(selected_features)} features จาก {len(X_train.columns)}")

3.ปรับปรุงการแบ่งข้อมูล
# ใช้ Time Series Split แทนการแบ่งแบบสุ่ม
from sklearn.model_selection import TimeSeriesSplit
# ในฟังก์ชัน train_scenario_model
tscv = TimeSeriesSplit(n_splits=5)
for train_index, test_index in tscv.split(X):
    X_train, X_test = X.iloc[train_index], X.iloc[test_index]
    y_train, y_test = y.iloc[train_index], y.iloc[test_index]
    # ใช้ fold สุดท้ายเป็น test set

4. ปรับปรุงการหา Threshold ที่เหมาะสม
# ขยายช่วงการค้นหา threshold และเพิ่มความละเอียด
def find_best_threshold_on_val(model, scaler, val_df, model_features, 
                              thresholds=np.arange(0.1, 0.95, 0.025)):
    # ปรับปรุงเกณฑ์การประเมิน threshold โดยเน้น expectancy มากขึ้น
    best_threshold = 0.5
    best_metric = -float('inf')
    
    for threshold in thresholds:
        # ทดสอบ threshold
        trade_df = create_trade_cycles_with_model(val_df, model, scaler, model_features, 
                                                model_confidence_threshold=threshold)
        
        # คำนวณ expectancy และ win rate
        if trade_df is not None and len(trade_df) >= min_trades:
            win_rate = trade_df['Profit'].apply(lambda x: 1 if x > 0 else 0).mean()
            avg_win = trade_df[trade_df['Profit'] > 0]['Profit'].mean() if len(trade_df[trade_df['Profit'] > 0]) > 0 else 0
            avg_loss = abs(trade_df[trade_df['Profit'] < 0]['Profit'].mean()) if len(trade_df[trade_df['Profit'] < 0]) > 0 else 0
            
            # คำนวณ expectancy = win_rate * avg_win - (1-win_rate) * avg_loss
            expectancy = (win_rate * avg_win) - ((1-win_rate) * avg_loss) if avg_loss > 0 else 0
            
            # ให้น้ำหนักกับ expectancy มากกว่า win_rate
            metric = (expectancy * 0.7) + (win_rate * 0.3)
            
            if metric > best_metric:
                best_metric = metric
                best_threshold = threshold

5. ปรับปรุงการประเมินโมเดล
# เพิ่มการประเมินด้วย trading metrics
def evaluate_model_with_trading_metrics(model, X_test, y_test, test_df):
    # ประเมินด้วย ML metrics ปกติ
    y_pred = model.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    
    # ประเมินด้วย trading metrics
    trade_df = create_trade_cycles_with_model(test_df, model, scaler, model_features)
    
    if trade_df is not None and len(trade_df) > 0:
        win_rate = trade_df['Profit'].apply(lambda x: 1 if x > 0 else 0).mean()
        profit_factor = abs(trade_df[trade_df['Profit'] > 0]['Profit'].sum() / 
                          trade_df[trade_df['Profit'] < 0]['Profit'].sum()) if trade_df[trade_df['Profit'] < 0]['Profit'].sum() != 0 else float('inf')
        expectancy = trade_df['Profit'].mean()
        
        print(f"📊 Trading Metrics: Win Rate={win_rate:.2f}, Profit Factor={profit_factor:.2f}, Expectancy={expectancy:.4f}")
        
        # คำนวณ combined score ที่ให้น้ำหนักกับ trading metrics มากกว่า
        combined_score = (accuracy * 0.3) + (win_rate * 0.3) + (min(profit_factor, 3) / 3 * 0.2) + (max(min(expectancy, 0.01), 0) / 0.01 * 0.2)
        
        return {
            'accuracy': accuracy,
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'expectancy': expectancy,
            'combined_score': combined_score
        }

6. เพิ่มการทำ Ensemble Models
# สร้าง ensemble จากโมเดลหลายประเภท
def create_ensemble_model(X_train, y_train):
    # สร้างโมเดลหลายประเภท
    models = {
        'lgbm': LGBMClassifier(random_state=42, **lgbm_params),
        'rf': RandomForestClassifier(random_state=42, n_estimators=100),
        'xgb': XGBClassifier(random_state=42, n_estimators=100)
    }
    
    # เทรนแต่ละโมเดล
    for name, model in models.items():
        model.fit(X_train, y_train)
        
    # สร้างฟังก์ชัน ensemble prediction
    def ensemble_predict_proba(X):
        probas = []
        for name, model in models.items():
            if hasattr(model, 'predict_proba'):
                proba = model.predict_proba(X)
                probas.append(proba)
        
        # เฉลี่ยความน่าจะเป็นจากทุกโมเดล
        return np.mean(probas, axis=0)
    
    return ensemble_predict_proba, models

7. ปรับปรุงการจัดการ Missing Values
# เพิ่มการจัดการ missing values ที่ซับซ้อนขึ้น
from sklearn.impute import KNNImputer

# ในฟังก์ชัน prepare_scenario_data
# ตรวจสอบและจัดการ missing values
missing_cols = X.columns[X.isna().any()].tolist()
if missing_cols:
    print(f"⚠️ พบ missing values ใน {len(missing_cols)} columns")
    
    # ใช้ KNN Imputer แทน simple imputation
    imputer = KNNImputer(n_neighbors=5)
    X_imputed = imputer.fit_transform(X)
    X = pd.DataFrame(X_imputed, columns=X.columns)
    print(f"✅ เติม missing values ด้วย KNN Imputer")

ช่วยจัดการอย่างเป็นลำดับ เพื่อให้ตรวจสอบได้ง่ายสำหรับฉันด้วย

++++++

ช่วยตรวจสอบส่วนที่มีการแก้ไขทั้งหมดอย่างละเอียด อีกครั้ง
1. ลำดับการจัดวาง code และการใช้งาน 
2. การเรียกใช้งาน ที่มีการบันทึกจากข้อ 4.
3. การทำงานกับส่วนอื่นๆ ที่เกี่ยวข้อง
4. การบันทึกผลลัพท์ เพื่อเรียกใช้งานครั้งต่อไป
5. การบันทึกที่เปิดดูได้ง่าย .txt << ถ้าคิดว่าจำเป็น
6. การแสดงผล << ถ้าคิดว่าจำเป็น
7. วิธีตรวจสอบเมื่อมี error ต่างๆ และการกำหนดค่าเริ่มต้น เพื่อป้องกัน